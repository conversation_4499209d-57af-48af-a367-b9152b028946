<script>
  import { onMount } from 'svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import WorkloadEditForm from '$lib/components/WorkloadEditForm.svelte';
  import WorkloadAreaEditForm from '$lib/components/WorkloadAreaEditForm.svelte';

  /** @type {import('./$types').PageData} */
  export let data;

  // Component state
  const computerId = $page.url.searchParams.get('computerId') || '';
  let rowOrder = $page.url.searchParams.get('rowOrder') || 'FirstLast';
  let workloadData = [];
  let workloadAreas = [];
  let selectedWorkloadAreaId = null;
  let gridData = [];
  let isLoading = true;
  let error = null;

  // ServiceCodeAndActionType state
  let serviceCodeActionTypes = [];
  let isLoadingServiceCodeActionTypes = true;
  let serviceCodeActionTypesError = null;

  // Get product designation from URL if available
  let computerProductDesignation = $page.url.searchParams.get('productDesignation') || '';
  let productDesignation = computerProductDesignation;

  // 3D Service Schedule state
  let serviceSchedule = [];
  let isCalculatingSchedule = false;

  // Edit modal state
  let isEditModalOpen = false;
  let currentWorkload = null;
  let isWorkloadAreaModalOpen = false;
  let currentWorkloadArea = null;
  let isSaving = false;

  // Workload generation form state
  let showGenerationForm = false;
  let startYear = new Date().getFullYear();
  let startMonth = 1;
  let endYear = new Date().getFullYear();
  let endMonth = 12;
  let isGenerating = false;
  let generationError = null;

  // Bulk update form state
  let showBulkUpdateForm = false;
  let bulkStartYear = new Date().getFullYear();
  let bulkStartMonth = 1;
  let bulkEndYear = new Date().getFullYear();
  let bulkEndMonth = 12;
  let bulkHours = 0;
  let bulkActivity = 'Idle';
  let isBulkUpdating = false;
  let bulkUpdateError = null;

  // Get available years for dropdowns (current year and 5 years before/after)
  const currentYear = new Date().getFullYear();
  const availableYears = Array.from({ length: 11 }, (_, i) => currentYear - 5 + i);

  // Get month names for display
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const monthNamesShort = [
    'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
    'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
  ];

  // Activity types for dropdowns
  const activityTypes = [
    'Idle',
    'Contract Service',
    'Emergency Service',
    'Preventive Maintenance',
    'Installation',
    'Consulting',
    'Other'
  ];

  // Open edit modal for a cell
  function openEditModal(year, month, existingData = null) {
    currentWorkload = existingData || {
      computerId,
      year,
      month,
      hours: 0,
      activity: 'Idle',
      type: 'Soft',
      description: '',
      workloadAreaId: selectedWorkloadAreaId
    };
    isEditModalOpen = true;
  }

  // Close edit modal
  function closeEditModal() {
    isEditModalOpen = false;
    currentWorkload = null;
  }

  // Open workload area edit modal
  function openWorkloadAreaModal(existingArea = null) {
    currentWorkloadArea = existingArea || {
      computerId,
      name: '',
      description: ''
    };
    isWorkloadAreaModalOpen = true;
  }

  // Close workload area edit modal
  function closeWorkloadAreaModal() {
    isWorkloadAreaModalOpen = false;
    currentWorkloadArea = null;
  }

  // Select a workload area
  function selectWorkloadArea(areaId) {
    selectedWorkloadAreaId = areaId;
    fetchWorkloadData();
  }

  // Save workload data
  async function saveWorkload(workload) {
    try {
      isSaving = true;
      error = null;

      // Prepare the data to send with proper type conversion
      const dataToSend = {
        computerId: workload.computerId || computerId,
        year: Number(workload.year) || new Date().getFullYear(),
        month: Number(workload.month) || 1,
        hours: Number(workload.hours) || 0,
        activity: workload.activity || 'Contract Service',
        type: workload.type || 'Soft',
        description: workload.description || '',
        isFixed: workload.type === 'Fixed',
        workloadAreaId: workload.workloadAreaId || null
      };

      const url = '/api/workload';

      console.log('Saving workload data:', JSON.stringify(dataToSend, null, 2));

      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSend)
      });

      console.log('Response status:', response.status, response.statusText);

      let result;
      const responseText = await response.text();

      try {
        result = responseText ? JSON.parse(responseText) : {};
        console.log('API Response:', result);
      } catch (jsonError) {
        console.error('Error parsing JSON response:', jsonError);
        throw new Error('Invalid response from server');
      }

      if (!response.ok) {
        const errorMsg = result?.error || result?.details || `HTTP ${response.status} - ${response.statusText}`;
        console.error('API Error:', {
          status: response.status,
          error: errorMsg,
          details: result?.details
        });

        // Format error message for display
        let errorMessage = 'Failed to save workload';
        if (Array.isArray(errorMsg)) {
          errorMessage = errorMsg.join('\n');
        } else if (typeof errorMsg === 'string') {
          errorMessage = errorMsg;
        }

        throw new Error(errorMessage);
      }

      if (result.success && result.data) {
        console.log(`Workload ${result.action || 'saved'} successfully:`, result);

        // Update the local data with the saved workload
        const updatedWorkload = result.data;
        const workloadIndex = workloadData.findIndex(w =>
          w.year === updatedWorkload.year &&
          w.month === updatedWorkload.month
        );

        if (workloadIndex !== -1) {
          // Update existing entry
          workloadData[workloadIndex] = {
            ...workloadData[workloadIndex],
            ...updatedWorkload
          };
        } else {
          // Add new entry
          workloadData = [...workloadData, updatedWorkload];
        }

        // Update the grid data
        updateGridData();

        // Recalculate service schedule based on updated accumulated hours
        calculateServiceSchedule();

        // Show success message
        const actionMessage = result.action === 'created' ? 'created' : 'updated';
        alert(`Workload ${actionMessage} successfully`);

        // Close the modal
        closeEditModal();
      } else {
        console.error('API reported failure:', result);
        throw new Error(result.error || 'Failed to save workload');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error saving workload:', errorMessage, err);
      error = errorMessage;
      alert(`Error: ${errorMessage}`);
    } finally {
      isSaving = false;
    }
  }

  // Function to fetch workload data
  async function fetchWorkloadData() {
    try {
      isLoading = true;
      error = null;

      // Build URL with query parameters
      let url = `/api/workload?computerId=${computerId}`;
      if (selectedWorkloadAreaId) {
        url += `&workloadAreaId=${selectedWorkloadAreaId}`;
      }

      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`Failed to fetch workload data: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('API Response:', result);

      if (result.success) {
        // Ensure dates are properly parsed
        workloadData = result.data.map(item => ({
          ...item,
          createdAt: item.createdAt ? new Date(item.createdAt) : null,
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : null
        }));
      } else {
        throw new Error(result.error || 'Failed to load workload data');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching workload data:', errorMessage, err);
      error = errorMessage;
    } finally {
      isLoading = false;
    }
  }

  // Function to fetch workload areas
  async function fetchWorkloadAreas() {
    try {
      error = null;

      const response = await fetch(`/api/workload-area?computerId=${computerId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch workload areas: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Workload Areas API Response:', result);

      if (result.success) {
        // Ensure dates are properly parsed
        workloadAreas = result.data.map(item => ({
          ...item,
          createdAt: item.createdAt ? new Date(item.createdAt) : null,
          updatedAt: item.updatedAt ? new Date(item.updatedAt) : null
        }));
      } else {
        throw new Error(result.error || 'Failed to load workload areas');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching workload areas:', errorMessage, err);
      error = errorMessage;
    }
  }

  // Save workload area
  async function saveWorkloadArea(workloadArea) {
    try {
      isSaving = true;
      error = null;

      const dataToSend = {
        id: workloadArea.id,
        computerId: workloadArea.computerId || computerId,
        name: workloadArea.name,
        description: workloadArea.description || ''
      };

      const response = await fetch('/api/workload-area', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(dataToSend)
      });

      if (!response.ok) {
        const responseText = await response.text();
        let result;
        try {
          result = responseText ? JSON.parse(responseText) : {};
        } catch (jsonError) {
          throw new Error('Invalid response from server');
        }

        const errorMsg = result?.error || result?.details || `HTTP ${response.status} - ${response.statusText}`;
        throw new Error(typeof errorMsg === 'string' ? errorMsg : JSON.stringify(errorMsg));
      }

      const result = await response.json();

      if (result.success) {
        // Refresh workload areas
        await fetchWorkloadAreas();

        // Show success message
        const actionMessage = result.action === 'created' ? 'created' : 'updated';
        alert(`Workload area ${actionMessage} successfully`);

        // Close the modal
        closeWorkloadAreaModal();
      } else {
        throw new Error(result.error || 'Failed to save workload area');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error saving workload area:', errorMessage, err);
      error = errorMessage;
      alert(`Error: ${errorMessage}`);
    } finally {
      isSaving = false;
    }
  }

  // Update grid data based on workloadData
  function updateGridData() {
    // Group by year
    const years = new Set(workloadData.map(entry => entry.year));

    // Create grid data array
    const newGridData = Array.from(years).sort((a, b) => b - a).map(year => {
      const yearEntries = workloadData.filter(entry => entry.year === year);
      const months = Array(12).fill(null);

      yearEntries.forEach(entry => {
        months[entry.month - 1] = entry;
      });

      return {
        year,
        months
      };
    });

    gridData = newGridData;

    // Recalculate service schedule based on updated accumulated hours
    calculateServiceSchedule();
  }

  // Generate workload items for the selected date range
  async function generateWorkloadItems() {
    try {
      isGenerating = true;
      generationError = null;

      // Validate date range
      const startDate = new Date(startYear, startMonth - 1);
      const endDate = new Date(endYear, endMonth - 1);

      if (endDate < startDate) {
        generationError = 'End date must be after start date';
        return;
      }

      // Calculate number of months to generate
      const months = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        months.push({
          year: currentDate.getFullYear(),
          month: currentDate.getMonth() + 1
        });

        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      console.log(`Generating ${months.length} workload items from ${startMonth}/${startYear} to ${endMonth}/${endYear}`);

      // Create workload items for each month
      const createdItems = [];

      for (const { year, month } of months) {
        const workloadData = {
          computerId,
          year,
          month,
          hours: 0, // Default hours
          activity: 'Idle', // Default activity is now Idle
          type: 'Soft', // Default type
          description: `Auto-generated workload for ${monthNames[month - 1]} ${year}`,
          workloadAreaId: selectedWorkloadAreaId
        };

        // Save to database
        const response = await fetch('/api/workload', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(workloadData)
        });

        if (!response.ok) {
          const result = await response.json();
          throw new Error(result.error || `Failed to create workload for ${month}/${year}`);
        }

        const result = await response.json();

        if (result.success) {
          createdItems.push(result.data);
        }
      }

      console.log(`Successfully created ${createdItems.length} workload items`);

      // Refresh the data
      await fetchWorkloadData();

      // Show success message
      alert(`Successfully created ${createdItems.length} workload items`);

    } catch (err) {
      console.error('Error generating workload items:', err);
      generationError = err instanceof Error ? err.message : 'Unknown error generating workload items';
    } finally {
      isGenerating = false;
    }
  }

  // Bulk update workload items for the selected date range
  async function bulkUpdateWorkloadItems() {
    try {
      isBulkUpdating = true;
      bulkUpdateError = null;

      // Validate date range
      const startDate = new Date(bulkStartYear, bulkStartMonth - 1);
      const endDate = new Date(bulkEndYear, bulkEndMonth - 1);

      if (endDate < startDate) {
        bulkUpdateError = 'End date must be after start date';
        return;
      }

      // Calculate number of months to update
      const months = [];
      let currentDate = new Date(startDate);

      while (currentDate <= endDate) {
        months.push({
          year: currentDate.getFullYear(),
          month: currentDate.getMonth() + 1
        });

        // Move to next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }

      console.log(`Updating ${months.length} workload items from ${bulkStartMonth}/${bulkStartYear} to ${bulkEndMonth}/${bulkEndYear}`);
      console.log(`Setting hours: ${bulkHours}, activity: ${bulkActivity}`);

      // Update workload items for each month
      const updatedItems = [];
      const createdItems = [];

      for (const { year, month } of months) {
        // Check if workload item exists for this month
        const existingItem = workloadData.find(item =>
          item.year === year &&
          item.month === month &&
          (selectedWorkloadAreaId === null || item.workloadAreaId === selectedWorkloadAreaId)
        );

        // Prepare workload data
        const workloadItemData = {
          computerId,
          year,
          month,
          hours: bulkHours,
          activity: bulkActivity,
          type: 'Soft', // Default type
          description: existingItem?.description || `Updated workload for ${monthNames[month - 1]} ${year}`,
          workloadAreaId: selectedWorkloadAreaId
        };

        // If item exists and is not fixed type, update it
        if (existingItem && existingItem.type !== 'Fixed') {
          // Update existing item
          const response = await fetch('/api/workload', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              ...workloadItemData,
              id: existingItem.id
            })
          });

          if (!response.ok) {
            const result = await response.json();
            throw new Error(result.error || `Failed to update workload for ${month}/${year}`);
          }

          const result = await response.json();

          if (result.success) {
            updatedItems.push(result.data);
          }
        } else if (!existingItem) {
          // Create new item
          const response = await fetch('/api/workload', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify(workloadItemData)
          });

          if (!response.ok) {
            const result = await response.json();
            throw new Error(result.error || `Failed to create workload for ${month}/${year}`);
          }

          const result = await response.json();

          if (result.success) {
            createdItems.push(result.data);
          }
        }
        // Skip fixed items
      }

      console.log(`Successfully updated ${updatedItems.length} and created ${createdItems.length} workload items`);

      // Refresh the data
      await fetchWorkloadData();

      // Show success message
      alert(`Successfully updated ${updatedItems.length} and created ${createdItems.length} workload items`);

      // Close the form
      showBulkUpdateForm = false;

    } catch (err) {
      console.error('Error updating workload items:', err);
      bulkUpdateError = err instanceof Error ? err.message : 'Unknown error updating workload items';
    } finally {
      isBulkUpdating = false;
    }
  }

  // Update grid data when workloadData changes
  $: if (workloadData.length > 0) {
    updateGridData();
  }

  // Function to fetch computer's product designation
  async function fetchComputerProductDesignation() {
    try {
      // If we already have the product designation from the URL, use it
      if (computerProductDesignation) {
        console.log('Using product designation from URL:', computerProductDesignation);

        // Fetch service code action types using the designation from URL
        fetchServiceCodeActionTypes(computerProductDesignation);
        return;
      }

      // Otherwise, fetch it from the API
      const response = await fetch(`/api/product-designation/${computerId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch product designation: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Product Designation API Response:', result);

      if (result.success) {
        // Check for both productDesignation and productValidityGroup fields
        computerProductDesignation = result.productValidityGroup || result.productDesignation || '';
        console.log('Using product designation/validity group from API:', computerProductDesignation);

        // After getting the product designation, fetch the service code action types
        if (computerProductDesignation && computerProductDesignation !== 'Unknown') {
          fetchServiceCodeActionTypes(computerProductDesignation);
        } else {
          isLoadingServiceCodeActionTypes = false;
          serviceCodeActionTypesError = 'No product designation or validity group found for this computer';
        }
      } else {
        throw new Error(result.error || 'Failed to load product designation');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching product designation:', errorMessage, err);
      serviceCodeActionTypesError = errorMessage;
      isLoadingServiceCodeActionTypes = false;
    }
  }

  // Function to fetch service code action types based on product designation
  async function fetchServiceCodeActionTypes(productDesignation) {
    try {
      isLoadingServiceCodeActionTypes = true;
      serviceCodeActionTypesError = null;

      const response = await fetch(`/api/service-code-action-types/by-product-validity/${encodeURIComponent(productDesignation)}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch service code action types: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log('Service Code Action Types API Response:', result);

      // Store the service code action types
      serviceCodeActionTypes = result;

      // Calculate service schedule based on accumulated hours
      calculateServiceSchedule();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      console.error('Error fetching service code action types:', errorMessage, err);
      serviceCodeActionTypesError = errorMessage;
    } finally {
      isLoadingServiceCodeActionTypes = false;
    }
  }

  // Calculate when each service code action should occur based on accumulated hours
  function calculateServiceSchedule() {
    try {
      isCalculatingSchedule = true;

      // Reset service schedule
      serviceSchedule = [];

      // If we don't have workload data, return
      if (!workloadData || workloadData.length === 0) {
        return;
      }

      // Define service thresholds based on the TAD1640-42GE-B product
      // These are the specific service intervals for this product
      const serviceThresholds = [
        {
          serviceCode: '1770-39-00-10',
          actionType: 'Inspect',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'S',
          hours: 1000
        },
        {
          serviceCode: '1770-39-00-20',
          actionType: 'Clean',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'A',
          hours: 500
        },
        {
          serviceCode: '1770-39-00-20',
          actionType: 'Inspect',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'A',
          hours: 500
        },
        {
          serviceCode: '1770-39-00-20',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'A',
          partNumber: '23075367',
          hours: 500
        },
        {
          serviceCode: '1770-39-00-20',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'A',
          partNumber: '23075366',
          hours: 500
        },
        {
          serviceCode: '1770-39-00-20',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'A',
          hours: 500
        },
        {
          serviceCode: '1770-39-00-30',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'B',
          partNumber: '20998367',
          hours: 1000
        },
        {
          serviceCode: '1770-39-00-30',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'B',
          partNumber: '21702911',
          hours: 1000
        },
        {
          serviceCode: '1770-39-00-30',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'B',
          partNumber: '22460372',
          hours: 1000
        },
        {
          serviceCode: '1770-39-00-40',
          actionType: 'Inspect',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'C',
          hours: 2000
        },
        {
          serviceCode: '1770-39-00-50',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'D',
          partNumber: '20430376',
          hours: 2000
        },
        {
          serviceCode: '1770-39-00-50',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'D',
          partNumber: '3838617',
          hours: 2000
        },
        {
          serviceCode: '1770-39-00-60',
          actionType: 'Replace',
          activityPurpose: 'ServiceAndMaintenance',
          serviceActivityLabel: 'E',
          hours: 8000
        }
      ];

      // Create a flat array of all months with accumulated hours in chronological order
      const monthsWithAccHours = [];

      // Get all years from workload data
      const years = [...new Set(workloadData.map(item => item.year))].sort((a, b) => a - b);

      // Create a flat array of all months across all years in chronological order
      years.forEach(year => {
        for (let month = 1; month <= 12; month++) {
          const workloadItem = workloadData.find(d => d.year === year && d.month === month);

          if (workloadItem && workloadItem.accHours !== undefined) {
            monthsWithAccHours.push({
              year,
              month,
              monthName: monthNames[month - 1],
              accHours: workloadItem.accHours,
              monthlyHours: workloadItem.hours || 0,
              date: new Date(year, month - 1, 15) // Middle of the month for date calculations
            });
          }
        }
      });

      // Sort months chronologically
      monthsWithAccHours.sort((a, b) => a.date.getTime() - b.date.getTime());

      if (monthsWithAccHours.length === 0) {
        return; // No months with data
      }

      // Process each service threshold to find the exact month when it should occur
      serviceThresholds.forEach(service => {
        const serviceHours = service.hours;

        // Find all occurrences of this service based on accumulated hours
        let previousServiceHours = 0;
        let nextServiceHours = serviceHours; // First service at initial interval

        // Check each month to see if a service is due
        for (let i = 0; i < monthsWithAccHours.length; i++) {
          const currentMonth = monthsWithAccHours[i];
          const currentAccHours = currentMonth.accHours;

          // If we've reached or passed the next service hour threshold
          if (currentAccHours >= nextServiceHours) {
            // Find the exact month where the threshold was crossed
            // This is either the current month or we need to find the closest month
            let serviceMonth = currentMonth;

            // If we're not at the first month, check if the previous month was closer to the threshold
            if (i > 0) {
              const prevMonth = monthsWithAccHours[i - 1];
              const currentDiff = Math.abs(currentAccHours - nextServiceHours);
              const prevDiff = Math.abs(prevMonth.accHours - nextServiceHours);

              // If the previous month was closer to the threshold, use it instead
              if (prevDiff < currentDiff && prevMonth.accHours >= nextServiceHours) {
                serviceMonth = prevMonth;
              }
            }

            // Add this service to the schedule
            serviceSchedule.push({
              serviceCode: service.serviceCode,
              actionType: service.actionType,
              activityPurpose: service.activityPurpose,
              serviceActivityLabel: service.serviceActivityLabel,
              partNumber: service.partNumber,
              targetHours: serviceHours,
              scheduledMonth: serviceMonth.month,
              scheduledYear: serviceMonth.year,
              scheduledMonthName: serviceMonth.monthName,
              actualAccHours: serviceMonth.accHours,
              scheduledBy: 'hours',
              lastPerformedAt: previousServiceHours,
              nextDueAt: nextServiceHours + serviceHours,
              exactMatch: serviceMonth.accHours === nextServiceHours
            });

            // Update for next service interval
            previousServiceHours = nextServiceHours;
            nextServiceHours += serviceHours;
          }
        }
      });

      // Add time-based service intervals if serviceCodeActionTypes is available
      if (serviceCodeActionTypes && serviceCodeActionTypes.length > 0) {
        serviceCodeActionTypes.forEach(serviceItem => {
          // Skip items without months information
          if (!serviceItem.InternalNoOfMonths || serviceItem.InternalNoOfMonths <= 0) {
            return;
          }

          const intervalMonths = serviceItem.InternalNoOfMonths;

          // Only process if we have at least one month with data
          if (monthsWithAccHours.length > 0) {
            // Start from the first month with data
            const startMonth = monthsWithAccHours[0];
            let currentDate = new Date(startMonth.year, startMonth.month - 1, 15);

            // Find the last month with data to set an end date
            const lastMonth = monthsWithAccHours[monthsWithAccHours.length - 1];
            const endDate = new Date(lastMonth.year, lastMonth.month - 1, 15);

            // Schedule services at regular month intervals
            while (currentDate <= endDate) {
              // Find the closest month data to this date
              const closestMonthData = findClosestMonthData(monthsWithAccHours, currentDate);

              if (closestMonthData) {
                serviceSchedule.push({
                  serviceCode: serviceItem.ServiceCode,
                  actionType: serviceItem.ActionType,
                  activityPurpose: serviceItem.ActivityPurpose,
                  serviceActivityLabel: serviceItem.ServiceActivityLabel,
                  partNumber: serviceItem.PartNumber,
                  targetMonths: intervalMonths,
                  scheduledMonth: closestMonthData.month,
                  scheduledYear: closestMonthData.year,
                  scheduledMonthName: closestMonthData.monthName,
                  actualAccHours: closestMonthData.accHours,
                  scheduledBy: 'months'
                });
              }

              // Move to next interval
              currentDate.setMonth(currentDate.getMonth() + intervalMonths);
            }
          }
        });
      }

      // Sort service schedule by scheduled year and month
      serviceSchedule.sort((a, b) => {
        if (a.scheduledYear !== b.scheduledYear) {
          return a.scheduledYear - b.scheduledYear;
        }
        return a.scheduledMonth - b.scheduledMonth;
      });

      console.log('Calculated service schedule:', serviceSchedule);
    } catch (err) {
      console.error('Error calculating service schedule:', err);
    } finally {
      isCalculatingSchedule = false;
    }
  }

  // Helper function to find the closest month data to a given date
  function findClosestMonthData(monthsData, targetDate) {
    if (!monthsData || monthsData.length === 0) return null;

    let closestMonth = monthsData[0];
    let minDiff = Math.abs(closestMonth.date.getTime() - targetDate.getTime());

    for (let i = 1; i < monthsData.length; i++) {
      const diff = Math.abs(monthsData[i].date.getTime() - targetDate.getTime());
      if (diff < minDiff) {
        minDiff = diff;
        closestMonth = monthsData[i];
      }
    }

    return closestMonth;
  }

  // Fetch data when component mounts
  onMount(() => {
    if (!computerId) {
      error = 'Computer ID is required';
      isLoading = false;
      return;
    }

    // Fetch workload areas first, then workload data
    fetchWorkloadAreas().then(() => fetchWorkloadData());

    // Fetch computer's product designation and related service code action types
    fetchComputerProductDesignation();

    // Cleanup function
    return () => {
      // Any cleanup if needed
    };
  });

  // We already defined monthNames and monthNamesShort above

  // Precompute the grid data
  $: {
    if (workloadData) {
      console.log('Raw workloadData:', workloadData);
      if (workloadData.length === 0) {
        gridData = [];
      } else {
        // Get unique years and sort based on rowOrder
        const years = [...new Set(workloadData.map(item => item.year))];

        // Sort years based on the selected order
        const sortedYears = years.sort((a, b) => {
          return rowOrder === 'FirstLast' ? a - b : b - a;
        });

        // Calculate accumulated hours across all months and years
        let accumulatedHours = 0;

        // Create a flat array of all year/month combinations in chronological order
        const allMonths = [];

        // If FirstLast order (chronological), process years from earliest to latest
        // If LastFirst order (reverse chronological), still process months chronologically for AccHours
        const chronologicalYears = [...sortedYears].sort((a, b) => a - b);

        // Create a flat array of all months across all years
        chronologicalYears.forEach(year => {
          for (let month = 1; month <= 12; month++) {
            allMonths.push({ year, month });
          }
        });

        // Calculate accumulated hours for each month
        const accHoursMap = new Map();

        allMonths.forEach(({ year, month }) => {
          const workloadItem = workloadData.find(d => d.year === year && d.month === month);

          if (workloadItem) {
            accumulatedHours += workloadItem.hours || 0;
          }

          // Store the accumulated hours for this year/month
          accHoursMap.set(`${year}-${month}`, accumulatedHours);
        });

        // Create grid data with months and accumulated hours
        gridData = sortedYears.map(year => ({
          year,
          months: Array(12).fill(0).map((_, i) => {
            const monthIndex = i + 1;
            const workloadItem = workloadData.find(d => d.year === year && d.month === monthIndex);

            if (workloadItem) {
              // Add accumulated hours to the workload item
              return {
                ...workloadItem,
                accHours: accHoursMap.get(`${year}-${monthIndex}`) || 0
              };
            }

            return null;
          })
        }));

        console.log(`Computed gridData (${rowOrder}):`, gridData);
      }
    }
  }
</script>

<div class="container">
  <div class="header">
    <button
      class="back-button"
      on:click={() => history.back()}
    >
      ← Back
    </button>

    <div class="header-center">
      <h1>Workload Overview</h1>
      {#if data?.computer}
        <div class="computer-info-inline">
          <span class="computer-info-item">
            <span class="info-label">ID:</span>
            <span class="info-value">{data.computer._id}</span>
          </span>
          <span class="computer-info-item">
            <span class="info-label">Name:</span>
            <span class="info-value">{data.computer.name || 'Unnamed Computer'}</span>
          </span>
        </div>
      {/if}
    </div>

    <div class="header-controls">
      {#if workloadData && workloadData.length > 0}
        <button
          type="button"
          class="control-button bulk-update-btn"
          on:click={() => showBulkUpdateForm = !showBulkUpdateForm}
        >
          {showBulkUpdateForm ? 'Hide Bulk' : 'Bulk Update'}
        </button>

        <button
          type="button"
          class="control-button service-plan-btn"
          on:click={() => {
            const url = `/service-plan-report/${computerId}?productDesignation=${productDesignation}`;
            window.open(url, '_blank');
          }}
        >
          📋 Service Plan
        </button>

        <button
          type="button"
          class="control-button service-offer-btn"
          on:click={() => {
            const url = `/service-contract-offer?computerId=${computerId}&productDesignation=${productDesignation}`;
            goto(url);
          }}
        >
          📄 Create Service Offer
        </button>
      {/if}

      <div class="sort-controls">
        <span class="sort-label">Sort:</span>
        <button
          class="sort-button"
          class:active={rowOrder === 'FirstLast'}
          on:click={() => {
            rowOrder = 'FirstLast';
            const url = new URL(window.location.href);
            url.searchParams.set('rowOrder', 'FirstLast');
            window.history.pushState({}, '', url);
          }}
        >
          First→Last
        </button>
        <button
          class="sort-button"
          class:active={rowOrder === 'LastFirst'}
          on:click={() => {
            rowOrder = 'LastFirst';
            const url = new URL(window.location.href);
            url.searchParams.set('rowOrder', 'LastFirst');
            window.history.pushState({}, '', url);
          }}
        >
          Last→First
        </button>
      </div>
    </div>
  </div>



  {#if error}
    <div class="error-message">
      Error: {error}
    </div>
  {:else if isLoading}
    <div class="loading">Loading workload data for computer ID: {computerId}...</div>
  {:else if !workloadData || workloadData.length === 0}
    <div class="no-data-container">
      <div class="no-data">No workload data available for this computer</div>

      <div class="workload-generation-form">
        <h2>Create Workload Items</h2>
        <p>Select a date range to generate workload items:</p>

        {#if generationError}
          <div class="error-message">
            Error: {generationError}
          </div>
        {/if}

        <div class="form-grid">
          <div class="form-group">
            <label for="startYear">Start Year</label>
            <select id="startYear" bind:value={startYear} class="form-control">
              {#each availableYears as year}
                <option value={year}>{year}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="startMonth">Start Month</label>
            <select id="startMonth" bind:value={startMonth} class="form-control">
              {#each monthNames as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="endYear">End Year</label>
            <select id="endYear" bind:value={endYear} class="form-control">
              {#each availableYears as year}
                <option value={year}>{year}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="endMonth">End Month</label>
            <select id="endMonth" bind:value={endMonth} class="form-control">
              {#each monthNames as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="button"
            class="generate-button"
            on:click={generateWorkloadItems}
            disabled={isGenerating}
          >
            {isGenerating ? 'Generating...' : 'Generate Workload Items'}
          </button>
        </div>
      </div>
    </div>
  {:else}
    {#if showBulkUpdateForm}
      <div class="bulk-update-form">
        <h2>Bulk Update Workload Items</h2>
        <p>Set hours and activity for a date range (Fixed items will not be updated):</p>

        {#if bulkUpdateError}
          <div class="error-message">
            Error: {bulkUpdateError}
          </div>
        {/if}

        <div class="form-grid">
          <div class="form-group">
            <label for="bulkStartYear">Start Year</label>
            <select id="bulkStartYear" bind:value={bulkStartYear} class="form-control">
              {#each availableYears as year}
                <option value={year}>{year}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="bulkStartMonth">Start Month</label>
            <select id="bulkStartMonth" bind:value={bulkStartMonth} class="form-control">
              {#each monthNames as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="bulkEndYear">End Year</label>
            <select id="bulkEndYear" bind:value={bulkEndYear} class="form-control">
              {#each availableYears as year}
                <option value={year}>{year}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="bulkEndMonth">End Month</label>
            <select id="bulkEndMonth" bind:value={bulkEndMonth} class="form-control">
              {#each monthNames as month, i}
                <option value={i + 1}>{month}</option>
              {/each}
            </select>
          </div>

          <div class="form-group">
            <label for="bulkHours">Hours</label>
            <input
              id="bulkHours"
              type="number"
              min="0"
              step="0.5"
              bind:value={bulkHours}
              class="form-control"
            />
          </div>

          <div class="form-group">
            <label for="bulkActivity">Activity</label>
            <select id="bulkActivity" bind:value={bulkActivity} class="form-control">
              {#each activityTypes as type}
                <option value={type}>{type}</option>
              {/each}
            </select>
          </div>
        </div>

        <div class="form-actions">
          <button
            type="button"
            class="update-button"
            on:click={bulkUpdateWorkloadItems}
            disabled={isBulkUpdating}
          >
            {isBulkUpdating ? 'Updating...' : 'Update Workload Items'}
          </button>
        </div>
      </div>
    {/if}
    <div class="workload-grid">
      <div class="debug-info" style="display: none;">
        Computer ID: {computerId}<br>
        Data points: {workloadData.length}
      </div>
      <div class="grid-header">Year</div>
      {#each Array(12) as _, i}
        <div class="month-header">{monthNames[i]}</div>
      {/each}

      {#each gridData as { year, months }}
        <div class="year-cell">{year}</div>
        {#each months as monthData, i}
          <div class="data-cell {monthData ? 'has-data' : ''}">
            {#if monthData}
              <div class="hours">{monthData.hours}h</div>
              <div class="acc-hours" title="Accumulated Hours">
                Acc: {Math.round(monthData.accHours)}
              </div>
              <div class="activity" title={monthData.activity}>
                {monthData.activity}
              </div>
              <div class="type-badge {monthData.type.toLowerCase()}">
                {monthData.type}
              </div>

              <button
                class="edit-button"
                on:click|stopPropagation={() => openEditModal(year, i + 1, monthData)}
                aria-label="Edit workload"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                  <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                  <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                </svg>
              </button>
            {:else}
              <button
                type="button"
                class="empty-cell"
                on:click|stopPropagation={() => openEditModal(year, i + 1)}
                aria-label="Add workload for ${monthNames[i]} ${year}"
              >
                <span>+ Add</span>
              </button>
            {/if}
          </div>
        {/each}
      {/each}
    </div>
  {/if}

  <!-- Edit Modal -->
  {#if currentWorkload}
    <WorkloadEditForm
      bind:isOpen={isEditModalOpen}
      workload={currentWorkload}
      on:save={async (e) => {
        await saveWorkload(e.detail.workload);
      }}
      on:cancel={closeEditModal}
    />
  {/if}

  <!-- Workload Area Edit Modal -->
  {#if currentWorkloadArea}
    <WorkloadAreaEditForm
      bind:isOpen={isWorkloadAreaModalOpen}
      workloadArea={currentWorkloadArea}
      on:save={async (e) => {
        await saveWorkloadArea(e.detail.workloadArea);
      }}
      on:cancel={closeWorkloadAreaModal}
    />
  {/if}



  <!-- Service Code and Action Type List -->
  <div class="service-code-action-types-container">
    <h2>Service Codes and Action Types</h2>
    <p>Based on Computer's Product Validity Group: <strong>{computerProductDesignation || 'Unknown'}</strong></p>

    {#if serviceCodeActionTypesError}
      <div class="error-message">
        Error: {serviceCodeActionTypesError}
      </div>
    {:else if isLoadingServiceCodeActionTypes}
      <div class="loading">Loading service code action types...</div>
    {:else if !serviceCodeActionTypes || serviceCodeActionTypes.length === 0}
      <div class="no-data">No service code action types available for this computer's product designation</div>
    {:else}
      <div class="service-code-action-types-grid">
        <div class="grid-header">Service Code</div>
        <div class="grid-header">Action Type</div>
        <div class="grid-header">Activity Purpose</div>
        <div class="grid-header">Service Activity Label</div>
        <div class="grid-header">Part Number</div>
        <div class="grid-header">Hours</div>

        {#each serviceCodeActionTypes as item}
          <div class="grid-cell">{item.ServiceCode}</div>
          <div class="grid-cell">{item.ActionType}</div>
          <div class="grid-cell">{item.ActivityPurpose}</div>
          <div class="grid-cell">{item.ServiceActivityLabel}</div>
          <div class="grid-cell">{item.PartNumber}</div>
          <div class="grid-cell">{item.InternalNoOfHours}</div>
        {/each}
      </div>
    {/if}
  </div>

  <!-- Labour Time Data Section -->
  <div class="labour-time-container">
    <h2>Labour Time Data</h2>
    <p>Labour time information for Product Designation: <strong>{data.productDesignation || 'Unknown'}</strong> | Computer Category: <strong>{data.computerCategory || 'Unknown'}</strong></p>

    {#if data.labourTimeData && data.labourTimeData.length > 0}
      <div class="labour-time-grid">
        <div class="grid-header">Service Code</div>
        <div class="grid-header">Service Description</div>
        <div class="grid-header">VST Code</div>
        <div class="grid-header">VST Hours</div>
        <div class="grid-header">Service Phase</div>
        <div class="grid-header">Computer Category</div>

        {#each data.labourTimeData as labour}
          <div class="grid-cell">{labour.ServiceCode || 'N/A'}</div>
          <div class="grid-cell" title="{labour.ServiceDescription}">{labour.ServiceDescription || 'N/A'}</div>
          <div class="grid-cell">{labour.VSTCode || 'N/A'}</div>
          <div class="grid-cell labour-hours">{labour.VSTHours || 0}</div>
          <div class="grid-cell">{labour.ServicePhase || 'N/A'}</div>
          <div class="grid-cell">{labour.ComputerCategory || 'N/A'}</div>
        {/each}
      </div>

      <div class="labour-time-summary">
        <p><strong>Total Labour Time Records:</strong> {data.labourTimeData.length}</p>
        <p><strong>Total VST Hours:</strong> {data.labourTimeData.reduce((sum, item) => sum + (item.VSTHours || 0), 0).toFixed(1)}</p>
      </div>
    {:else}
      <div class="no-data">No labour time data available for this computer's category or product designation</div>
    {/if}
  </div>
</div>

<style>
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem;
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 0.75rem 1rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
  }

  .header-center {
    flex: 1;
    text-align: center;
  }

  .header-center h1 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #1e293b;
  }

  .computer-info-inline {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    padding: 0.375rem 0.75rem;
    background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
    border-radius: 0.25rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  .computer-info-item {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }

  .info-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: rgba(255, 255, 255, 0.9);
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .info-value {
    font-size: 0.8rem;
    font-weight: 600;
    color: white;
  }

  .computer-info-item:first-child .info-value {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 0.75rem;
  }

  .header-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }

  .sort-controls {
    display: flex;
    align-items: center;
    gap: 0.375rem;
  }

  .sort-label {
    font-size: 0.75rem;
    font-weight: 500;
    color: #64748b;
    margin-right: 0.25rem;
  }

  .sort-button {
    padding: 0.25rem 0.5rem;
    border: 1px solid #e2e8f0;
    border-radius: 0.25rem;
    background: white;
    cursor: pointer;
    transition: all 0.2s;
    font-size: 0.75rem;
    font-weight: 500;
  }

  .sort-button:hover {
    background: #f8fafc;
    border-color: #cbd5e1;
  }

  .sort-button.active {
    background: #1e40af;
    color: white;
    border-color: #1e40af;
  }

  .sort-button:focus {
    outline: 2px solid #93c5fd;
    outline-offset: 2px;
  }

  .control-button {
    padding: 0.375rem 0.75rem;
    background-color: #1e40af;
    color: white;
    border: none;
    border-radius: 0.25rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    font-size: 0.8rem;
  }

  .control-button:hover {
    background-color: #1e3a8a;
  }



  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: stretch;
      gap: 0.75rem;
    }

    .header-center {
      text-align: left;
    }

    .computer-info-inline {
      justify-content: flex-start;
      gap: 1rem;
    }

    .header-controls {
      justify-content: space-between;
      flex-wrap: wrap;
      gap: 0.5rem;
    }

    .sort-controls {
      gap: 0.25rem;
    }

    .sort-button {
      padding: 0.25rem 0.375rem;
      font-size: 0.7rem;
    }

    .control-button {
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
    }
  }

  .back-button {
    background: none;
    border: none;
    color: #3b82f6;
    cursor: pointer;
    font-size: 1rem;
    margin-right: 1rem;
    padding: 0.5rem 0;
  }

  .back-button:hover {
    text-decoration: underline;
  }

  h1 {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
  }

  .workload-grid {
    display: grid;
    grid-template-columns: repeat(13, minmax(80px, 1fr));
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    width: 100%;
    font-size: 0.85rem;
  }

  @media (max-width: 1200px) {
    .workload-grid {
      grid-template-columns: repeat(13, minmax(70px, 1fr));
      font-size: 0.8rem;
    }
  }

  @media (max-width: 1024px) {
    .workload-grid {
      grid-template-columns: repeat(13, minmax(60px, 1fr));
      font-size: 0.75rem;
    }
  }

  .grid-header,
  .month-header,
  .year-cell,
  .data-cell {
    background-color: white;
    padding: 0.5rem 0.25rem;
    text-align: center;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .grid-header,
  .month-header {
    font-weight: 600;
    background-color: #f3f4f6;
  }

  .month-header {
    padding: 0.5rem 0.25rem;
    font-weight: 500;
  }

  .year-cell {
    font-weight: 600;
    background-color: #f9fafb;
  }

  .data-cell {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 70px;
    padding: 0.25rem;
    position: relative;
    transition: background-color 0.2s ease;
    cursor: pointer;

    &:hover {
      background-color: #f8fafc;
    }

    .empty-cell {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      color: #718096;
      font-size: 0.8rem;
      opacity: 0.7;
      transition: all 0.2s;
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      font-family: inherit;

      &:hover {
        color: #4299e1;
        opacity: 1;
      }
    }

    .edit-button {
      position: absolute;
      top: 0.1rem;
      right: 0.1rem;
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid #e2e8f0;
      border-radius: 0.25rem;
      width: 1.25rem;
      height: 1.25rem;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      opacity: 0;
      transition: all 0.2s;
      color: #718096;

      svg {
        width: 0.75rem;
        height: 0.75rem;
      }

      &:hover {
        background: #fff;
        color: #4299e1;
        border-color: #cbd5e0;
      }
    }

    &:hover .edit-button {
      opacity: 1;
    }

    &.has-data {
      background-color: #f0f9ff;

      &:hover {
        background-color: #e0f2fe;
      }
    }

    .hours {
      font-weight: 600;
      font-size: 0.9rem;
      color: #0369a1;
      margin-bottom: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: center;
    }

    .acc-hours {
      font-weight: 500;
      font-size: 0.75rem;
      color: #0891b2; /* Teal color for accumulated hours */
      margin-bottom: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      text-align: center;
      font-style: italic;
    }

    .activity {
      font-size: 0.7rem;
      color: #334155;
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      margin-bottom: 0.1rem;
    }

    .type-badge {
      font-size: 0.6rem;
      padding: 0.1rem 0.25rem;
      border-radius: 0.2rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.02em;
      margin-top: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;

      &.fixed {
        background-color: #fee2e2;
        color: #dc2626;
        font-weight: 600;
      }

      &.soft {
        background-color: #dbeafe;
        color: #1e40af;
      }
    }

    .area-badge {
      font-size: 0.6rem;
      padding: 0.1rem 0.25rem;
      border-radius: 0.2rem;
      font-weight: 500;
      text-transform: uppercase;
      letter-spacing: 0.02em;
      margin-top: 0.1rem;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
      background-color: #f0fdfa;
      color: #0f766e;
      text-align: center;
    }
  }

  .error-message {
    color: #ef4444;
    padding: 1rem;
    background-color: #fef2f2;
    border-radius: 0.375rem;
    margin-top: 1rem;
  }

  .loading {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
  }

  .no-data-container {
    padding: 2rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-top: 1rem;
  }

  .no-data {
    text-align: center;
    color: #6b7280;
    margin-bottom: 2rem;
    font-style: italic;
  }

  .workload-generation-form {
    max-width: 800px;
    margin: 0 auto;
    padding: 1.5rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .workload-generation-form h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }

  .workload-generation-form p {
    margin-bottom: 1.5rem;
    color: #64748b;
  }

  .form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .form-group label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #475569;
  }

  .form-control {
    padding: 0.5rem;
    border: 1px solid #cbd5e1;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #1e293b;
    background-color: white;
  }

  .form-control:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  }

  .form-actions {
    display: flex;
    justify-content: center;
    margin-top: 1rem;
  }

  .generate-button {
    padding: 0.75rem 1.5rem;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 200px;
  }

  .generate-button:hover {
    background-color: #2563eb;
  }

  .generate-button:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }

  /* Bulk update form styles */

  .bulk-update-form {
    max-width: 800px;
    margin: 0 auto 2rem;
    padding: 1.5rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .bulk-update-form h2 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }

  .bulk-update-form p {
    margin-bottom: 1.5rem;
    color: #64748b;
  }

  .update-button {
    padding: 0.75rem 1.5rem;
    background-color: #1e40af; /* Dark blue */
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
    min-width: 200px;
  }

  .update-button:hover {
    background-color: #1e3a8a; /* Darker blue */
  }

  .update-button:disabled {
    background-color: #93c5fd;
    cursor: not-allowed;
  }

  /* 3D Service Schedule styles */
  .service-schedule-container {
    margin-top: 3rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .service-schedule-container h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }

  .service-schedule-container p {
    margin-bottom: 1rem;
    color: #64748b;
  }

  .schedule-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .legend-section {
    flex: 1;
    min-width: 250px;
  }

  .legend-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #334155;
    margin-bottom: 0.75rem;
    padding-bottom: 0.25rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .legend-items {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
  }

  .legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .legend-badge {
    width: 2rem;
    height: 0.75rem;
    border-radius: 0.25rem;
  }

  .legend-badge.exact-match {
    background-color: #10b981;
  }

  .legend-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
  }

  .legend-icon.hours {
    background-color: #3b82f6;
    color: white;
  }

  .legend-icon.months {
    background-color: #f59e0b;
    color: white;
  }

  .legend-text {
    font-size: 0.85rem;
    color: #475569;
    font-weight: 500;
  }

  .legend-label {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    background-color: #f1f5f9;
    color: #334155;
    border: 1px solid #94a3b8;
  }

  .service-schedule-3d {
    position: relative;
    padding: 2rem 0;
    perspective: 1000px;
    perspective-origin: 50% 50%;
  }

  .timeline {
    position: relative;
    min-height: 400px;
    padding: 2rem 0;
  }

  .timeline-line {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 4px;
    background: linear-gradient(to bottom, #3b82f6, #1e40af);
    transform: translateX(-50%);
    border-radius: 2px;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.3);
  }

  .timeline-item {
    position: relative;
    margin-bottom: 3rem;
    --offset: calc(30px * var(--item-index, 0));
    transform-style: preserve-3d;
    animation: fadeIn 0.5s ease-out forwards;
    animation-delay: calc(0.1s * var(--item-index, 0));
    opacity: 0;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .timeline-marker {
    position: absolute;
    top: 0;
    left: 50%;
    width: 30px;
    height: 30px;
    background-color: #3b82f6;
    border-radius: 50%;
    transform: translateX(-50%);
    z-index: 2;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
  }

  .timeline-marker.hours {
    background-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.2);
  }

  .timeline-marker.months {
    background-color: #f59e0b;
    box-shadow: 0 0 0 4px rgba(245, 158, 11, 0.2);
  }

  .marker-icon {
    font-size: 14px;
    line-height: 1;
  }

  .timeline-item:hover .timeline-marker {
    transform: translateX(-50%) scale(1.2);
  }

  .timeline-content {
    position: relative;
    width: calc(50% - 40px);
    margin-left: calc(50% + 40px);
    transform: translateZ(var(--offset));
    transition: transform 0.3s ease;
  }

  .timeline-item:nth-child(even) .timeline-content {
    margin-left: 0;
    margin-right: calc(50% + 40px);
  }

  .timeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .timeline-date {
    font-weight: 600;
    color: #1e40af;
  }

  .timeline-hours {
    font-weight: 500;
    color: #0891b2;
    font-style: italic;
  }

  .timeline-card {
    position: relative;
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #3b82f6;
    transition: all 0.3s ease;
    transform: rotateY(0deg);
  }

  .service-activity-label {
    position: absolute;
    top: -15px;
    right: -15px;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #3b82f6;
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 5;
  }

  /* Service activity label colors based on service type */
  .service-activity-label.label-A {
    background-color: #10b981; /* Green */
  }

  .service-activity-label.label-B {
    background-color: #3b82f6; /* Blue */
  }

  .service-activity-label.label-C {
    background-color: #8b5cf6; /* Purple */
  }

  .service-activity-label.label-D {
    background-color: #f59e0b; /* Amber */
  }

  .service-activity-label.label-E {
    background-color: #ef4444; /* Red */
  }

  .service-activity-label.label-S {
    background-color: #6b7280; /* Gray */
  }

  .service-activity-label.label-default {
    background-color: #94a3b8; /* Slate */
  }

  .timeline-item:nth-child(even) .timeline-card {
    border-left: none;
    border-right: 4px solid #3b82f6;
  }

  .timeline-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .service-code {
    font-weight: 600;
    font-size: 1.1rem;
    color: #1e293b;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 0.5rem;
  }

  .service-badge-container {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    margin-bottom: 0.75rem;
  }

  .service-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .service-badge.hours {
    background-color: #dbeafe;
    color: #1e40af;
  }

  .service-badge.months {
    background-color: #fef3c7;
    color: #92400e;
  }

  .service-badge.exact-match {
    background-color: #dcfce7;
    color: #047857;
  }

  .timeline-card.exact-match {
    border-left-color: #10b981;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
  }

  .timeline-item:nth-child(even) .timeline-card.exact-match {
    border-right-color: #10b981;
    border-left: none;
  }

  .service-details {
    display: grid;
    gap: 0.5rem;
  }

  .service-label {
    font-weight: 500;
    color: #334155;
  }

  .service-purpose {
    color: #64748b;
    font-size: 0.9rem;
  }

  .part-number {
    font-family: monospace;
    background-color: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.85rem;
    color: #475569;
    display: inline-block;
  }

  .target-hours {
    font-weight: 500;
    color: #0891b2;
    margin-top: 0.25rem;
  }

  .target-months {
    font-weight: 500;
    color: #92400e;
    margin-top: 0.25rem;
  }

  .next-due {
    font-weight: 500;
    color: #047857;
    margin-top: 0.25rem;
    font-style: italic;
  }

  .last-performed {
    font-weight: 500;
    color: #6b7280;
    margin-top: 0.25rem;
    font-style: italic;
  }

  /* Service Code Action Types styles */
  .service-code-action-types-container {
    margin-top: 3rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .service-code-action-types-container h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }

  .service-code-action-types-container p {
    margin-bottom: 1.5rem;
    color: #64748b;
  }

  .service-code-action-types-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    width: 100%;
    font-size: 0.85rem;
    margin-top: 1rem;
  }

  .service-code-action-types-grid .grid-header {
    font-weight: 600;
    background-color: #f3f4f6;
    padding: 0.75rem 0.5rem;
    text-align: left;
  }

  .service-code-action-types-grid .grid-cell {
    background-color: white;
    padding: 0.75rem 0.5rem;
    text-align: left;
    border-bottom: 1px solid #f3f4f6;
  }

  .service-code-action-types-grid .grid-cell:nth-child(even) {
    background-color: #f9fafb;
  }

  /* Labour Time styles */
  .labour-time-container {
    margin-top: 3rem;
    padding: 1.5rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }

  .labour-time-container h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #1e293b;
  }

  .labour-time-container p {
    margin-bottom: 1.5rem;
    color: #64748b;
  }

  .labour-time-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 1px;
    background-color: #e5e7eb;
    border: 1px solid #e5e7eb;
    width: 100%;
    font-size: 0.85rem;
    margin-top: 1rem;
  }

  .labour-time-grid .grid-header {
    font-weight: 600;
    background-color: #f3f4f6;
    padding: 0.75rem 0.5rem;
    text-align: left;
  }

  .labour-time-grid .grid-cell {
    background-color: white;
    padding: 0.75rem 0.5rem;
    text-align: left;
    border-bottom: 1px solid #f3f4f6;
    word-wrap: break-word;
    overflow-wrap: break-word;
  }

  .labour-time-grid .grid-cell:nth-child(even) {
    background-color: #f9fafb;
  }

  .labour-time-grid .labour-hours {
    font-weight: 600;
    color: #0891b2;
    text-align: center;
  }

  .labour-time-summary {
    margin-top: 1rem;
    padding: 1rem;
    background-color: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
  }

  .labour-time-summary p {
    margin: 0.25rem 0;
    color: #475569;
    font-size: 0.9rem;
  }

  @media (max-width: 1024px) {
    .labour-time-grid {
      grid-template-columns: repeat(3, 1fr);
      font-size: 0.8rem;
    }

    .labour-time-grid .grid-cell:nth-child(4),
    .labour-time-grid .grid-cell:nth-child(5),
    .labour-time-grid .grid-cell:nth-child(6) {
      display: none;
    }

    .labour-time-grid .grid-header:nth-child(4),
    .labour-time-grid .grid-header:nth-child(5),
    .labour-time-grid .grid-header:nth-child(6) {
      display: none;
    }
  }

  @media (max-width: 768px) {
    .labour-time-grid {
      grid-template-columns: repeat(2, 1fr);
      font-size: 0.75rem;
    }

    .labour-time-grid .grid-cell:nth-child(3),
    .labour-time-grid .grid-cell:nth-child(4),
    .labour-time-grid .grid-cell:nth-child(5),
    .labour-time-grid .grid-cell:nth-child(6) {
      display: none;
    }

    .labour-time-grid .grid-header:nth-child(3),
    .labour-time-grid .grid-header:nth-child(4),
    .labour-time-grid .grid-header:nth-child(5),
    .labour-time-grid .grid-header:nth-child(6) {
      display: none;
    }
  }
</style>
