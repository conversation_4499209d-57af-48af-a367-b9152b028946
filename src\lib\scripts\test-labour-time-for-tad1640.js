import { MongoClient } from 'mongodb';

const uri = 'mongodb://localhost:27017';
const client = new MongoClient(uri);

async function testLabourTimeForTAD1640() {
    try {
        await client.connect();
        const db = client.db('ServiceContracts');
        const collection = db.collection('LabourTime');

        console.log('=== Testing Labour Time Data for TAD1640-42GE-B ===\n');

        // Test 1: Get all Labour Time records
        const allRecords = await collection.find({}).toArray();
        console.log(`Total Labour Time records in database: ${allRecords.length}`);

        // Test 2: Look for records that might match TAD1640-42GE-B
        const productDesignation = 'TAD1640-42GE-B';
        console.log(`\nSearching for records related to: ${productDesignation}`);

        // Test 3: Check what ComputerCategory values exist
        const categories = await collection.distinct('ComputerCategory');
        console.log('\nAvailable Computer Categories:');
        categories.forEach(cat => console.log(`- ${cat}`));

        // Test 4: Check what Service Codes exist
        const serviceCodes = await collection.distinct('Service Code');
        console.log(`\nTotal unique Service Codes: ${serviceCodes.length}`);
        console.log('First 10 Service Codes:');
        serviceCodes.slice(0, 10).forEach(code => console.log(`- ${code}`));

        // Test 5: Get sample records
        console.log('\nSample Labour Time records:');
        const sampleRecords = await collection.find({}).limit(5).toArray();
        sampleRecords.forEach((record, index) => {
            console.log(`\nRecord ${index + 1}:`);
            console.log(`  Service Code: ${record['Service Code']}`);
            console.log(`  Service Description: ${record['Service Description']}`);
            console.log(`  VST Code: ${record['VST Code']}`);
            console.log(`  VST Hours: ${record['VST Hours']}`);
            console.log(`  Service Phase: ${record.ServicePhase}`);
            console.log(`  Computer Category: ${record.ComputerCategory}`);
        });

        // Test 6: Check for specific computer category that might match
        // Extract potential category from product designation
        const potentialCategory = productDesignation.split('-')[0]; // TAD1640
        console.log(`\nSearching for category: ${potentialCategory}`);
        
        const categoryRecords = await collection.find({
            ComputerCategory: { $regex: potentialCategory, $options: 'i' }
        }).toArray();
        
        console.log(`Found ${categoryRecords.length} records matching category pattern`);

        // Test 7: Check for any records with similar patterns
        const patternRecords = await collection.find({
            $or: [
                { 'Service Code': { $regex: 'TAD', $options: 'i' } },
                { 'Service Description': { $regex: 'TAD', $options: 'i' } },
                { ComputerCategory: { $regex: 'TAD', $options: 'i' } }
            ]
        }).toArray();
        
        console.log(`\nFound ${patternRecords.length} records with TAD pattern`);
        if (patternRecords.length > 0) {
            console.log('Sample TAD-related records:');
            patternRecords.slice(0, 3).forEach((record, index) => {
                console.log(`\nTAD Record ${index + 1}:`);
                console.log(`  Service Code: ${record['Service Code']}`);
                console.log(`  Service Description: ${record['Service Description']}`);
                console.log(`  Computer Category: ${record.ComputerCategory}`);
                console.log(`  VST Hours: ${record['VST Hours']}`);
            });
        }

    } catch (error) {
        console.error('Error testing Labour Time data:', error);
    } finally {
        await client.close();
        console.log('\nDisconnected from MongoDB');
    }
}

// Run the test
testLabourTimeForTAD1640().catch(console.error);
