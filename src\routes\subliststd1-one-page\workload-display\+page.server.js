import { error } from '@sveltejs/kit';
import { ObjectId } from 'mongodb';
import { getCollection } from '$lib/db/mongo';

/** @typedef {import('mongodb').WithId<import('mongodb').Document>} MongoDocument */

/**
 * @typedef {Object} WorkloadData
 * @property {string} _id
 * @property {string} computerId
 * @property {number} year
 * @property {number} month
 * @property {number} hours
 * @property {string} type
 * @property {string} [description]
 * @property {Date} createdAt
 * @property {Date} updatedAt
 */

/**
 * @typedef {Object} LabourTimeData
 * @property {string} _id
 * @property {string} ServiceCode - Service Code
 * @property {string} ServiceDescription - Service Description
 * @property {string} VSTCode - VST Code
 * @property {number} VSTHours - VST Hours
 * @property {string} ServicePhase - Service Phase
 * @property {string} ComputerCategory - Computer Category
 */

/** @type {import('./$types').PageServerLoad} */
export async function load({ url }) {
  const computerId = url.searchParams.get('computerId');
  const productDesignation = url.searchParams.get('productDesignation');

  if (!computerId) {
    throw error(400, 'Computer ID is required');
  }

  if (!ObjectId.isValid(computerId)) {
    throw error(400, 'Invalid computer ID format');
  }

  try {
    // Get the computer details
    const computersColl = await getCollection('CustomerComputers');
    const computer = await computersColl.findOne({ _id: new ObjectId(computerId) });

    if (!computer) {
      throw error(404, 'Computer not found');
    }

    // Get the parent customer details
    const customersColl = await getCollection('Customers');
    const customer = await customersColl.findOne({ _id: computer.customerId });

    if (!customer) {
      throw error(404, 'Customer not found');
    }

    // Get workload data for the computer
    const workloadColl = await getCollection('Workload');
    const workloadCursor = workloadColl.find({
      computerId: new ObjectId(computerId)
    });

    // Transform the data to match our WorkloadData type
    const workloadData = (await workloadCursor.toArray()).map(doc => ({
      _id: doc._id.toString(),
      computerId: doc.computerId.toString(),
      year: doc.year,
      month: doc.month,
      hours: doc.hours,
      type: doc.type,
      description: doc.description || '',
      createdAt: doc.createdAt,
      updatedAt: doc.updatedAt
    }));

    // Get Labour Time data based on ProductDesignation or computer Category
    let labourTimeData = [];

    // Determine what to use for filtering Labour Time data
    const computerProductDesignation = productDesignation || computer.ProductDesignation || computer.productDesignation;
    let computerCategory = computer.Category || computer.ComputerCategory || computer.type;

    // If no category is set, try to extract it from the model field
    if (!computerCategory && computer.model) {
      const modelMatch = computer.model.match(/D\d+/i);
      if (modelMatch) {
        computerCategory = modelMatch[0].toUpperCase();
      }
    }

    try {
      const labourTimeColl = await getCollection('LabourTime');

      console.log('Fetching Labour Time data for:', {
        productDesignation: computerProductDesignation,
        computerCategory: computerCategory,
        computerModel: computer.model
      });

      // Try to get Labour Time data by ComputerCategory first
      if (computerCategory) {
        const labourTimeCursor = labourTimeColl.find({
          ComputerCategory: computerCategory
        }).sort({ 'Service Code': 1 });

        labourTimeData = (await labourTimeCursor.toArray()).map(doc => ({
          _id: doc._id.toString(),
          ServiceCode: doc['Service Code'] || '',
          ServiceDescription: doc['Service Description'] || '',
          VSTCode: doc['VST Code'] || '',
          VSTHours: doc['VST Hours'] || 0,
          ServicePhase: doc.ServicePhase || '',
          ComputerCategory: doc.ComputerCategory || ''
        }));

        console.log(`Found ${labourTimeData.length} Labour Time records for Category: ${computerCategory}`);
      }

      // If no data found by category, try to get some sample data
      if (labourTimeData.length === 0) {
        console.log('No Labour Time data found by category, getting sample data');
        const labourTimeCursor = labourTimeColl.find({}).sort({ 'Service Code': 1 }).limit(10);

        labourTimeData = (await labourTimeCursor.toArray()).map(doc => ({
          _id: doc._id.toString(),
          ServiceCode: doc['Service Code'] || '',
          ServiceDescription: doc['Service Description'] || '',
          VSTCode: doc['VST Code'] || '',
          VSTHours: doc['VST Hours'] || 0,
          ServicePhase: doc.ServicePhase || '',
          ComputerCategory: doc.ComputerCategory || ''
        }));

        console.log(`Found ${labourTimeData.length} sample Labour Time records`);
      }
    } catch (labourTimeError) {
      console.error('Error fetching Labour Time data:', labourTimeError);
      // Continue without Labour Time data
    }

    return {
      computer: {
        ...computer,
        _id: computer._id.toString(),
        customerId: computer.customerId.toString()
      },
      customer: {
        ...customer,
        _id: customer._id.toString()
      },
      workloadData,
      labourTimeData,
      productDesignation: productDesignation || computer.ProductDesignation || computer.productDesignation || '',
      computerCategory: computerCategory || ''
    };
  } catch (err) {
    console.error('Error in workload data load:', err);
    throw error(500, 'Failed to load workload data');
  }
}
